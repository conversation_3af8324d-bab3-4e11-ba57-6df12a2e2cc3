{"1000001235-2648421227_1-1750333003": ["今天是2025年6月19日，星期四，北京，晴", "我们来看一个多模态RAG方案，双线索机制，搞文本和图像embedding，然后套上Agent，讲个了不错的故事，看看具体思路。", "来看多模态RAG进展，之前的典型做法是基于视觉语言模型(VLM)的嵌入模型将相关页面嵌入并检索为图像，并使用可接受图像作为输入的VLM生成答案。", "《SimpleDoc:Multi-ModalDocumentUnderstandingwithDual-CuePageRetrievalandIterativeRefinement》（https://arxiv.org/pdf/2506.14035），代码在：https://github.com/ag2ai/SimpleDoc", "1、看亮点", "从方案上看，两个亮点：", "一个是双重提示检索：结合页面级视觉嵌入和LLM生成的摘要来检索和重新排序相关页面。", "一个是迭代推理：单个基于VLM的代理动态更新查询和工作记忆以迭代地完善答案。", "2、看实现细节", "实现思路很简单，可以看下实现细节：", "重点在标红部分，", "其中摘要用了个prompt实现：", "用一个具体的例子来说明具体过程，如下图：", "从中可以看到，SimpleDoc如何通过迭代推理来解决一个问题。在第一轮中，代理基于嵌入和基于摘要的过滤检索了第6页、第13页和第14页。然而，检索到的页面仅描述了实验设置和评估指标，而没有给出确切的对齐分数。", "代理识别出这一缺口，并生成了一个更精确的查询，专门请求一个比较不同温度下分数的部分或表格。这个更新后的查询检索到了第7页，其中包含了表3，里面有所需的信息，从而使代理能够正确回答温度0.1产生了最高的对齐分数。", "3、看结果", "结果上，在4个DocVQA数据集上的表现比之前的基准模型平均高出3.2%，按照它的讲法就是页面更少，准确度更高，在DocVQA基准测试中准确率高达70.12%，同时每个查询仅读取约3.5页。", "此外，方案简单但有效：在4个主要基准测试中的3个上，表现优于MDocAgent等多代理系统和M3DocRAG等混合RAG管道。", "1、https://arxiv.org/pdf/2506.14035", "老刘，NLP开源爱好者与践行者，主页：https://liuhuanyong.github.io。", "对大模型&知识图谱&RAG&文档理解感兴趣，并对每日早报、老刘说NLP历史线上分享、心得交流等感兴趣的，欢迎加入社区，社区持续纳新。", "加入社区方式：关注公众号，在后台菜单栏中点击会员社区加入。"], "1000002719-2649000095_1-1751792180": ["来源：机器之心编辑：杜伟、泽南", "没等来DeepSeek官方的R2，却迎来了一个速度更快、性能不弱于R1的「野生」变体！", "这两天，一个名为「DeepSeekR1T2」的模型火了！", "这个模型的速度比R1-0528快200%，比R1快20%。除了速度上的显著优势，它在GPQADiamond（专家级推理能力问答基准）和AIME24（数学推理基准）上的表现均优于R1，但未达到R1-0528的水平。", "在技术层面，采用了专家组合（AssemblyofExperts，AoE）技术开发，并融合了DeepSeek官方的V3、R1和R1-0528三大模型。", "当然，这个模型也是开源的，遵循MIT协议，并在HuggingFace上开放了权重。", "HuggingFace地址：https://huggingface.co/tngtech/DeepSeek-TNG-R1T2-Chimera", "经过进一步了解，我们发现：DeepSeekR1T2是德国一家AI咨询公司「TNG」推出的，模型全称为「DeepSeek-TNGR1T2Chimera」（以下简称R1T2）。", "该模型除了前文提到的在智力水平和输出效率之间实现完美平衡之外，相较于这家公司的初代模型「R1TChimera」，智力大幅跃升，并实现了突破性的think-token一致性。", "不仅如此，即使在没有任何系统提示的情况下，该模型也能表现稳定，提供自然的对话交互体验。", "在评论区，有人误以为这个模型出自DeepSeek官方，并且认为他们是不是也在走相同的路线：给模型起各种名称，就是不用主系列下一代版本号？", "更多的人认可该模型「找到了智能与输出token长度之间的最佳平衡点，并且提升了速度」，并对该模型在现实世界的表现充满了期待。", "模型细节概览", "从HuggingFace主页来看，R1T2是一个基于DeepSeekR1-0528、R1以及V3-0324模型构建的AoEChimera模型。", "该模型是一个采用DeepSeek-MoETransformer架构的大语言模型，参数规模为671B。", "R1T2是该公司4月26日发布的初代模型「R1TChimera」的首个迭代版本。相较于利用双基模型（V3-0324+R1）的初代架构，本次升级到了三心智（Tri-Mind）融合架构，新增基模型R1-0528。", "该模型采用AoE技术构建，过程中利用较高精度的直接脑区编辑（directbrainedits）实现。这种精密融合不仅带来全方位提升，更彻底解决了初代R1T的<think>token一致性缺陷。", "团队表示，R1T2对比其他模型具备如下优劣：", "与DeepSeekR1对比：R1T2有望成为R1的理想替代品，两者几乎可以通用，并且R1T2性能更佳，可直接替换。", "与R1-0528对比：如果不需要达到0528级别的最高智能，R1T2相比之下更加经济。", "与R1T对比：通常更建议使用R1T2，除非R1T的特定人格是最佳选择、思考token问题不重要，或者极度需求速度。", "与DeepSeekV3-0324对比：V3速度更快，如果不太关注智能可以选择V3；但是，如果需要推理能力，R1T2是首选。", "此外，R1T2的几点局限性表现在：", "R1-0528虽推理耗时更长，但在高难度基准测试中仍优于R1T2；", "经SpeechMap.ai（由xlr8harder提供）测评，R1T2应答克制度（reserved）显著高于R1T，但低于R1-0528；", "暂不支持函数调用：受R1基模型影响，现阶段不推荐函数调用密集型场景（后续版本可能修复）；", "基准测试变更说明：开发版由AIME24+MT-Bench变更为AIME24/25+GPQA-Diamond测评体系，新体系下R1与初代R1T的分差较早期公布数据更大。", "最后，关于R1T2中重要的AoE技术，可以参考以下论文。", "论文标题：AssemblyofExperts:Linear-timeconstructionoftheChimeraLLMvariantswithemergentandadaptablebehaviors", "论文地址：https://arxiv.org/pdf/2506.14794", "参考链接：https://x.com/tngtech/status/1940531045432283412"]}