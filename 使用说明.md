# 微信公众号聚合平台使用说明

## 🎯 项目简介

这个项目可以帮你自动爬取多个微信公众号的文章，进行智能去重，并生成博客文章。

## ✅ 环境准备

项目已经配置完成，包括：
- ✅ Python 3.13.5 环境
- ✅ 虚拟环境 (venv)
- ✅ 所有依赖包已安装
- ✅ 项目结构完整

## 🚀 快速开始

### 第一步：配置要爬取的公众号

编辑 `data/name2fakeid.json` 文件，添加你想爬取的公众号：

```json
{
    "人民日报": "",
    "新华社": "",
    "你想爬取的公众号名称": ""
}
```

### 第二步：获取微信公众平台认证信息

**重要：你需要有微信公众平台的访问权限**

1. 访问 [微信公众平台](https://mp.weixin.qq.com/)
2. 使用微信扫码登录
3. 登录成功后，从浏览器地址栏复制 `token=xxxxxxxxx` 中的token值
4. 按F12打开开发者工具
5. 点击 "Network" → "Fetch/XHR"
6. 刷新页面
7. 点击任意请求，在请求头中找到完整的 "Cookie" 值

### 第三步：配置认证信息

编辑 `data/id_info.json` 文件：

```json
{
    "token": "你的token值",
    "cookie": "你的完整cookie字符串"
}
```

### 第四步：运行项目

```bash
# 激活虚拟环境并运行
source venv/bin/activate
python3 main.py
```

## 🛠️ 使用配置工具

我为你创建了一个配置工具，可以更方便地管理项目：

```bash
source venv/bin/activate
python3 setup_guide.py
```

配置工具功能：
- 查看当前配置状态
- 添加新的公众号
- 更新认证信息
- 交互式配置界面

## 📁 输出文件说明

运行成功后，会在 `data/` 目录生成以下文件：

- `微信公众号聚合平台_按时间区分.md` - 按时间排序的文章汇总
- `微信公众号聚合平台_按公众号区分.md` - 按公众号分类的文章汇总
- `message_info.json` - 文章元信息
- `message_detail_text.json` - 文章详细内容
- `minhash_dict.pickle` - 去重算法数据

## 🔧 高级功能

### 自动去重
- 使用MinHash+LSH算法
- 自动识别重复和转载文章
- 准确率高达100%（0.9阈值）

### 自动登录
- token/cookie过期时自动重新登录
- 无需手动维护认证信息

### 博客集成
- 生成的Markdown文件可直接用于Hexo、Jekyll等博客系统
- 支持封面图片下载
- 自动格式化文章内容

## ⚠️ 注意事项

1. **权限要求**：需要微信公众平台的管理员权限或相关访问权限
2. **网络环境**：运行时建议关闭VPN等代理工具
3. **请求频率**：程序内置频率控制，避免被限制
4. **浏览器要求**：需要Chrome浏览器用于自动登录

## 🐛 常见问题

### Q: 运行时提示"invalid session"或"invalid csrf token"
A: token或cookie已过期，程序会自动打开浏览器重新登录

### Q: 爬取失败或没有数据
A: 检查网络连接和认证信息是否正确

### Q: 如何添加更多公众号？
A: 编辑 `data/name2fakeid.json` 文件或使用配置工具

## 📞 技术支持

如果遇到问题，可以：
1. 检查 `data/issues_message.json` 文件查看错误信息
2. 使用配置工具检查配置状态
3. 查看项目的GitHub页面获取更多信息

## 🎉 开始使用

现在你可以开始使用这个强大的微信公众号聚合平台了！

1. 配置你想爬取的公众号
2. 获取并配置认证信息
3. 运行 `python3 main.py`
4. 等待爬取完成
5. 查看生成的Markdown文件

祝你使用愉快！🚀
