Metadata-Version: 2.1
Name: datasketch
Version: 1.6.5
Summary: Probabilistic data structures for processing and searching very large datasets
Home-page: https://ekzhu.github.io/datasketch
Author: ekzhu
Author-email: <EMAIL>
License: MIT
Project-URL: Source, https://github.com/ekzhu/datasketch
Keywords: database datamining
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Topic :: Database
Classifier: Topic :: Scientific/Engineering :: Information Analysis
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
License-File: LICENSE
Requires-Dist: numpy >=1.11
Requires-Dist: scipy >=1.0.0
Provides-Extra: benchmark
Requires-Dist: pyhash >=0.9.3 ; extra == 'benchmark'
Requires-Dist: matplotlib >=3.1.2 ; extra == 'benchmark'
Requires-Dist: scikit-learn >=0.21.3 ; extra == 'benchmark'
Requires-Dist: scipy >=1.3.3 ; extra == 'benchmark'
Requires-Dist: pandas >=0.25.3 ; extra == 'benchmark'
Requires-Dist: SetSimilaritySearch >=0.1.7 ; extra == 'benchmark'
Requires-Dist: pyfarmhash >=0.2.2 ; extra == 'benchmark'
Requires-Dist: nltk >=3.4.5 ; extra == 'benchmark'
Provides-Extra: cassandra
Requires-Dist: cassandra-driver >=3.20 ; extra == 'cassandra'
Provides-Extra: experimental_aio
Requires-Dist: aiounittest ; (python_version >= "3.6") and extra == 'experimental_aio'
Requires-Dist: motor ; (python_version >= "3.6") and extra == 'experimental_aio'
Provides-Extra: redis
Requires-Dist: redis >=2.10.0 ; extra == 'redis'
Provides-Extra: test
Requires-Dist: cassandra-driver >=3.20 ; extra == 'test'
Requires-Dist: redis >=2.10.0 ; extra == 'test'
Requires-Dist: mock >=2.0.0 ; extra == 'test'
Requires-Dist: mockredispy ; extra == 'test'
Requires-Dist: coverage ; extra == 'test'
Requires-Dist: pymongo >=3.9.0 ; extra == 'test'
Requires-Dist: nose >=1.3.7 ; extra == 'test'
Requires-Dist: nose-exclude >=0.5.0 ; extra == 'test'
Requires-Dist: pytest ; extra == 'test'

datasketch: Big Data Looks Small
================================

.. image:: https://static.pepy.tech/badge/datasketch/month
    :target: https://pepy.tech/project/datasketch

.. image:: https://zenodo.org/badge/DOI/10.5281/zenodo.598238.svg
   :target: https://zenodo.org/doi/10.5281/zenodo.598238

datasketch gives you probabilistic data structures that can process and
search very large amount of data super fast, with little loss of
accuracy.

This package contains the following data sketches:

+-------------------------+-----------------------------------------------+
| Data Sketch             | Usage                                         |
+=========================+===============================================+
| `MinHash`_              | estimate Jaccard similarity and cardinality   |
+-------------------------+-----------------------------------------------+
| `Weighted MinHash`_     | estimate weighted Jaccard similarity          |
+-------------------------+-----------------------------------------------+
| `HyperLogLog`_          | estimate cardinality                          |
+-------------------------+-----------------------------------------------+
| `HyperLogLog++`_        | estimate cardinality                          |
+-------------------------+-----------------------------------------------+

The following indexes for data sketches are provided to support
sub-linear query time:

+---------------------------+-----------------------------+------------------------+
| Index                     | For Data Sketch             | Supported Query Type   |
+===========================+=============================+========================+
| `MinHash LSH`_            | MinHash, Weighted MinHash   | Jaccard Threshold      |
+---------------------------+-----------------------------+------------------------+
| `MinHash LSH Forest`_     | MinHash, Weighted MinHash   | Jaccard Top-K          |
+---------------------------+-----------------------------+------------------------+
| `MinHash LSH Ensemble`_   | MinHash                     | Containment Threshold  |
+---------------------------+-----------------------------+------------------------+
| `HNSW`_                   | Any                         | Custom Metric Top-K    |
+---------------------------+-----------------------------+------------------------+

datasketch must be used with Python 3.7 or above, NumPy 1.11 or above, and Scipy. 

Note that `MinHash LSH`_ and `MinHash LSH Ensemble`_ also support Redis and Cassandra 
storage layer (see `MinHash LSH at Scale`_).

Install
-------

To install datasketch using ``pip``:

::

    pip install datasketch

This will also install NumPy as dependency.

To install with Redis dependency:

::

    pip install datasketch[redis]

To install with Cassandra dependency:

::

    pip install datasketch[cassandra]


.. _`MinHash`: https://ekzhu.github.io/datasketch/minhash.html
.. _`Weighted MinHash`: https://ekzhu.github.io/datasketch/weightedminhash.html
.. _`HyperLogLog`: https://ekzhu.github.io/datasketch/hyperloglog.html
.. _`HyperLogLog++`: https://ekzhu.github.io/datasketch/hyperloglog.html#hyperloglog-plusplus
.. _`MinHash LSH`: https://ekzhu.github.io/datasketch/lsh.html
.. _`MinHash LSH Forest`: https://ekzhu.github.io/datasketch/lshforest.html
.. _`MinHash LSH Ensemble`: https://ekzhu.github.io/datasketch/lshensemble.html
.. _`Minhash LSH at Scale`: http://ekzhu.github.io/datasketch/lsh.html#minhash-lsh-at-scale
.. _`HNSW`: https://ekzhu.github.io/datasketch/documentation.html#hnsw
