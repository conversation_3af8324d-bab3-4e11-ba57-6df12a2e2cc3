datasketch-1.6.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
datasketch-1.6.5.dist-info/LICENSE,sha256=qmE9065XRIY-vzh0S1vaFb-ysV3RuwOGCpu85EISbhE,1072
datasketch-1.6.5.dist-info/METADATA,sha256=6lc0_QRcRcMqOJTBkBjqR16aXBVUc4S_yj-pLF2u3Tg,5760
datasketch-1.6.5.dist-info/RECORD,,
datasketch-1.6.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasketch-1.6.5.dist-info/WHEEL,sha256=GJ7t_kWBFywbagK5eo9IoUwLW6oyOeTKmQ-9iHFVNxQ,92
datasketch-1.6.5.dist-info/top_level.txt,sha256=FuPx-giu4SjzQK_KHb52dU2TZT-g7QSHv4Otwcd_JnI,11
datasketch/__init__.py,sha256=b5wjc0gA11GaWWV_RkLirwrHKOdWqFfx3aI1WhlAmgM,644
datasketch/__pycache__/__init__.cpython-313.pyc,,
datasketch/__pycache__/b_bit_minhash.cpython-313.pyc,,
datasketch/__pycache__/hashfunc.cpython-313.pyc,,
datasketch/__pycache__/hnsw.cpython-313.pyc,,
datasketch/__pycache__/hyperloglog.cpython-313.pyc,,
datasketch/__pycache__/hyperloglog_const.cpython-313.pyc,,
datasketch/__pycache__/lean_minhash.cpython-313.pyc,,
datasketch/__pycache__/lsh.cpython-313.pyc,,
datasketch/__pycache__/lshensemble.cpython-313.pyc,,
datasketch/__pycache__/lshensemble_partition.cpython-313.pyc,,
datasketch/__pycache__/lshforest.cpython-313.pyc,,
datasketch/__pycache__/minhash.cpython-313.pyc,,
datasketch/__pycache__/storage.cpython-313.pyc,,
datasketch/__pycache__/version.cpython-313.pyc,,
datasketch/__pycache__/weighted_minhash.cpython-313.pyc,,
datasketch/b_bit_minhash.py,sha256=3UqZzVcWcQpd8wCvMgcdd6Izc5ziEuXIzEUtKnYQ8Io,6502
datasketch/experimental/__init__.py,sha256=q1-NyJ7-jMAordCeycmlbhqPpAFkZUiRUqglWxNawWg,458
datasketch/experimental/__pycache__/__init__.cpython-313.pyc,,
datasketch/experimental/aio/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasketch/experimental/aio/__pycache__/__init__.cpython-313.pyc,,
datasketch/experimental/aio/__pycache__/lsh.cpython-313.pyc,,
datasketch/experimental/aio/__pycache__/storage.cpython-313.pyc,,
datasketch/experimental/aio/lsh.py,sha256=d6U28_C3vWkj0JKYbTYx4ryGd5n4Wc5f3jwWeNHw3CM,16045
datasketch/experimental/aio/storage.py,sha256=6mJiBAtWmWPj8znyjoPpQiZrg2jy1dE6x6ifgmY2iB0,16673
datasketch/hashfunc.py,sha256=aPcDOHS0YUVvGfn7fTPrg7aG0wCQgU7MgLb4BJYXcr0,646
datasketch/hnsw.py,sha256=u961r4W3HczqKhTT7rlQgw7QAqBQ4o1rKyXYhkBdwHk,42764
datasketch/hyperloglog.py,sha256=WqHDtwVJpdJSmkJo-UZ6PkGygSzwtqK9wq53wCjEuDk,13343
datasketch/hyperloglog_const.py,sha256=4bYQoibAc_tbTND03RTc5ceQT2JJ_HB7NYRZlpvjymQ,72545
datasketch/lean_minhash.py,sha256=cVnHtr4gMiblohEeUBinxCbTjufrPm1BotMsPpf3TQY,9976
datasketch/lsh.py,sha256=pVR8HtBrN1wVMLm5g52Lzwb846K5QXY9ql72BuZEXAY,22548
datasketch/lshensemble.py,sha256=5EheP-LxfuRsBeQ8e8QV9v4EstlmTrIhusql3PnmlAg,10949
datasketch/lshensemble_partition.py,sha256=Rk782BgMpQh63AoG-RxlEbDML3zMFtC4XcZcH6kFKP8,7367
datasketch/lshforest.py,sha256=fzmUQLGTYGYW_3YRD9fWiEhCNMz3kKKsYJNpXthtB4E,7320
datasketch/minhash.py,sha256=yLEV6FHcCyAdy2PPd_L3v0QN4CH5lKqhgXMebgabswo,15087
datasketch/storage.py,sha256=Hf1ZkYefMIb9N1O2dsVawIQ7QqAFpud32mX8RgsSpmg,38961
datasketch/version.py,sha256=LEtHSENWZLwKaCJasxjOIQSt5Y_OahmKZYacXCmyQtI,22
datasketch/weighted_minhash.py,sha256=q342SSX2Q09WJSxSQRH1x2M1X41p49wwQd4-YnxmiE4,8918
