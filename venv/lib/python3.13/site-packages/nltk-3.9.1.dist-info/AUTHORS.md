# Natural Language Toolkit (NLTK) Authors

## Original Authors

- <PERSON> <<EMAIL>>
- <PERSON> <<EMAIL>>
- <PERSON><PERSON> <<EMAIL>>

## Contributors

- <PERSON>
- <PERSON><PERSON>'
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON><PERSON><PERSON>
- <PERSON><PERSON><PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>-G<PERSON>er
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON><PERSON><PERSON>
- <PERSON>
- <PERSON>-<PERSON>
- <PERSON>
- <PERSON><PERSON><PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON> <https://github.com/tconroy>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>-on
- <PERSON><PERSON>
- <PERSON>
- <PERSON>
- <PERSON><PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON><PERSON>
- <PERSON>
- <PERSON>
- <PERSON><PERSON><PERSON>
- <PERSON>
- <PERSON><PERSON><PERSON>
- <PERSON><PERSON>
- <PERSON>
- <PERSON><PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON><PERSON><PERSON>
- <PERSON><PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- Denis Krusko
- Ilia Kurenkov
- Stefano Lattarini
- Pierre-François Laquerre
- Stefano Lattarini
- Haejoong Lee
- Jackson Lee
- Max Leonov
- Chris Liechti
- Hyuckin David Lim
- Tom Lippincott
- Peter Ljunglöf
- Alex Louden
- David Lukeš
- Joseph Lynch
- Nitin Madnani
- Felipe Madrigal
- Bjørn Mæland
- Dean Malmgren
- Christopher Maloof
- Rob Malouf
- Iker Manterola
- Carl de Marcken
- Mitch Marcus
- Torsten Marek
- Robert Marshall
- Marius Mather
- Duncan McGreggor
- David McClosky
- Xinfan Meng
- Dmitrijs Milajevs
- Matt Miller
- Margaret Mitchell
- Tomonori Nagano
- Jason Narad
- Shari A’aidil Nasruddin
- Lance Nathan
- Morten Neergaard
- David Nemeskey
- Eric Nichols
- Joel Nothman
- Alireza Nourian
- Alexander Oleynikov
- Pierpaolo Pantone
- Ted Pedersen
- Jacob Perkins
- Alberto Planas
- Ondrej Platek
- Alessandro Presta
- Qi Liu
- Martin Thorsen Ranang
- Michael Recachinas
- Brandon Rhodes
- Joshua Ritterman
- Will Roberts
- Stuart Robinson
- Carlos Rodriguez
- Lorenzo Rubio
- Alex Rudnick
- Jussi Salmela
- Geoffrey Sampson
- Kepa Sarasola
- Kevin Scannell
- Nathan Schneider
- Rico Sennrich
- Thomas Skardal
- Eric Smith
- Lynn Soe
- Rob Speer
- Peter Spiller
- Richard Sproat
- Ceri Stagg
- Peter Stahl
- Oliver Steele
- Thomas Stieglmaier
- Jan Strunk
- Liling Tan
- Claire Taylor
- Louis Tiao
- Steven Tomcavage
- Tiago Tresoldi
- Marcus Uneson
- Yu Usami
- Petro Verkhogliad
- Peter Wang
- Zhe Wang
- Charlotte Wilson
- Chuck Wooters
- Steven Xu
- Beracah Yankama
- Lei Ye (叶磊)
- Patrick Ye
- Geraldine Sim Wei Ying
- Jason Yoder
- Thomas Zieglier
- 0ssifrage
- ducki13
- kiwipi
- lade
- isnowfy
- onesandzeros
- pquentin
- wvanlint
- Álvaro Justen <https://github.com/turicas>
- bjut-hz
- Sergio Oller
- Izam Mohammed <https://github.com/izam-mohammed>
- Will Monroe
- Elijah Rippeth
- Emil Manukyan
- Casper Lehmann-Strøm
- Andrew Giel
- Tanin Na Nakorn
- Linghao Zhang
- Colin Carroll
- Heguang Miao
- Hannah Aizenman (story645)
- George Berry
- Adam Nelson
- J Richard Snape
- Alex Constantin <<EMAIL>>
- Tsolak Ghukasyan
- Prasasto Adi
- Safwan Kamarrudin
- Arthur Tilley
- Vilhjalmur Thorsteinsson
- Jaehoon Hwang <https://github.com/jaehoonhwang>
- Chintan Shah <https://github.com/chintanshah24>
- sbagan
- Zicheng Xu
- Albert Au Yeung <https://github.com/albertauyeung>
- Shenjian Zhao
- Deng Wang <https://github.com/lmatt-bit>
- Ali Abdullah
- Stoytcho Stoytchev
- Lakhdar Benzahia
- Kheireddine Abainia <https://github.com/xprogramer>
- Yibin Lin <https://github.com/yibinlin>
- Artiem Krinitsyn
- Björn Mattsson
- Oleg Chislov
- Pavan Gururaj Joshi <https://github.com/PavanGJ>
- Ethan Hill <https://github.com/hill1303>
- Vivek Lakshmanan
- Somnath Rakshit <https://github.com/somnathrakshit>
- Anlan Du
- Pulkit Maloo <https://github.com/pulkitmaloo>
- Brandon M. Burroughs <https://github.com/brandonmburroughs>
- John Stewart <https://github.com/free-variation>
- Iaroslav Tymchenko <https://github.com/myproblemchild>
- Aleš Tamchyna
- Tim Gianitsos <https://github.com/timgianitsos>
- Philippe Partarrieu <https://github.com/ppartarr>
- Andrew Owen Martin
- Adrian Ellis <https://github.com/adrianjellis>
- Nat Quayle Nelson <https://github.com/nqnstudios>
- Yanpeng Zhao <https://github.com/zhaoyanpeng>
- Matan Rak <https://github.com/matanrak>
- Nick Ulle <https://github.com/nick-ulle>
- Uday Krishna <https://github.com/udaykrishna>
- Osman Zubair <https://github.com/okz12>
- Viresh Gupta <https://github.com/virresh>
- Ondřej Cífka <https://github.com/cifkao>
- Iris X. Zhou <https://github.com/irisxzhou>
- Devashish Lal <https://github.com/BLaZeKiLL>
- Gerhard Kremer <https://github.com/GerhardKa>
- Nicolas Darr <https://github.com/ndarr>
- Hervé Nicol <https://github.com/hervenicol>
- Alexandre H. T. Dias <https://github.com/alexandredias3d>
- Daksh Shah <https://github.com/Daksh>
- Jacob Weightman <https://github.com/jacobdweightman>
- Bonifacio de Oliveira <https://github.com/Bonifacio2>
- Armins Bagrats Stepanjans <https://github.com/ab-10>
- Vassilis Palassopoulos <https://github.com/palasso>
- Ram Rachum <https://github.com/cool-RR>
- Or Sharir <https://github.com/orsharir>
- Denali Molitor <https://github.com/dmmolitor>
- Jacob Moorman <https://github.com/jdmoorman>
- Cory Nezin <https://github.com/corynezin>
- Matt Chaput
- Danny Sepler <https://github.com/dannysepler>
- Akshita Bhagia <https://github.com/AkshitaB>
- Pratap Yadav <https://github.com/prtpydv>
- Hiroki Teranishi <https://github.com/chantera>
- Ruben Cartuyvels <https://github.com/rubencart>
- Dalton Pearson <https://github.com/daltonpearson>
- Robby Horvath <https://github.com/robbyhorvath>
- Gavish Poddar <https://github.com/gavishpoddar>
- Saibo Geng <https://github.com/Saibo-creator>
- Ahmet Yildirim <https://github.com/RnDevelover>
- Yuta Nakamura <https://github.com/yutanakamura-tky>
- Adam Hawley <https://github.com/adamjhawley>
- Panagiotis Simakis <https://github.com/sp1thas>
- Richard Wang <https://github.com/richarddwang>
- Alexandre Perez-Lebel <https://github.com/aperezlebel>
- Fernando Carranza <https://github.com/fernandocar86>
- Martin Kondratzky <https://github.com/martinkondra>
- Heungson Lee <https://github.com/heungson>
- M.K. Pawelkiewicz <https://github.com/hamiltonianflow>
- Steven Thomas Smith <https://github.com/essandess>
- Jan Lennartz <https://github.com/Madnex>
- Tim Sockel <https://github.com/TiMauzi>
- Akihiro Yamazaki <https://github.com/zakkie>
- Ron Urbach <https://github.com/sharpblade4>
- Vivek Kalyan <https://github.com/vivekkalyan>
- Tom Strange https://github.com/strangetom

## Others whose work we've taken and included in NLTK, but who didn't directly contribute it:

### Contributors to the Porter Stemmer

- Martin Porter
- Vivake Gupta
- Barry Wilkins
- Hiranmay Ghosh
- Chris Emerson

### Authors of snowball arabic stemmer algorithm

- Assem Chelli
- Abdelkrim Aries
- Lakhdar Benzahia
