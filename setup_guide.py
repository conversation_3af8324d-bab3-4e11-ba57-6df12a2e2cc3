#!/usr/bin/env python3
# -*- coding:utf-8 -*-
"""
微信公众号聚合平台配置指南
使用此脚本来配置和测试项目
"""

import json
import os
from pathlib import Path

def setup_project():
    """配置项目的基本设置"""
    print("🚀 微信公众号聚合平台配置指南")
    print("=" * 50)
    
    # 检查数据目录
    data_dir = Path("data")
    if not data_dir.exists():
        data_dir.mkdir(exist_ok=True)
        print("✅ 创建数据目录")
    
    # 配置公众号列表
    print("\n📝 配置要爬取的公众号")
    print("当前配置的公众号：")
    
    name2fakeid_file = data_dir / "name2fakeid.json"
    if name2fakeid_file.exists():
        with open(name2fakeid_file, 'r', encoding='utf-8') as f:
            name2fakeid = json.load(f)
        
        for i, (name, fakeid) in enumerate(name2fakeid.items(), 1):
            status = "✅ 已配置" if fakeid else "⏳ 待获取ID"
            print(f"  {i}. {name} - {status}")
    
    # 检查认证配置
    print("\n🔐 检查认证配置")
    id_info_file = data_dir / "id_info.json"
    if id_info_file.exists():
        with open(id_info_file, 'r', encoding='utf-8') as f:
            id_info = json.load(f)
        
        token_status = "✅ 已配置" if id_info.get('token') else "❌ 未配置"
        cookie_status = "✅ 已配置" if id_info.get('cookie') else "❌ 未配置"
        
        print(f"  Token: {token_status}")
        print(f"  Cookie: {cookie_status}")
    else:
        print("  ❌ 认证文件不存在")
    
    print("\n" + "=" * 50)
    print("📋 使用步骤：")
    print("1. 配置要爬取的公众号名称（修改 data/name2fakeid.json）")
    print("2. 获取微信公众平台的token和cookie（修改 data/id_info.json）")
    print("3. 运行主程序：python main.py")
    print("\n🔗 获取token和cookie的方法：")
    print("1. 访问 https://mp.weixin.qq.com/")
    print("2. 扫码登录")
    print("3. 从地址栏获取token=xxxxxxxxx")
    print("4. 从开发者工具Network中获取Cookie")

def add_wechat_account():
    """添加新的公众号到配置中"""
    print("\n➕ 添加新公众号")
    account_name = input("请输入公众号名称: ").strip()
    
    if not account_name:
        print("❌ 公众号名称不能为空")
        return
    
    name2fakeid_file = Path("data/name2fakeid.json")
    
    # 读取现有配置
    if name2fakeid_file.exists():
        with open(name2fakeid_file, 'r', encoding='utf-8') as f:
            name2fakeid = json.load(f)
    else:
        name2fakeid = {}
    
    # 添加新公众号
    name2fakeid[account_name] = ""
    
    # 保存配置
    with open(name2fakeid_file, 'w', encoding='utf-8') as f:
        json.dump(name2fakeid, f, ensure_ascii=False, indent=4)
    
    print(f"✅ 已添加公众号: {account_name}")

def update_auth_info():
    """更新认证信息"""
    print("\n🔐 更新认证信息")
    
    token = input("请输入token: ").strip()
    cookie = input("请输入cookie: ").strip()
    
    if not token or not cookie:
        print("❌ token和cookie都不能为空")
        return
    
    id_info = {
        "token": token,
        "cookie": cookie
    }
    
    id_info_file = Path("data/id_info.json")
    with open(id_info_file, 'w', encoding='utf-8') as f:
        json.dump(id_info, f, ensure_ascii=False, indent=4)
    
    print("✅ 认证信息已更新")

def main():
    """主函数"""
    while True:
        print("\n" + "=" * 50)
        print("微信公众号聚合平台 - 配置工具")
        print("=" * 50)
        print("1. 查看当前配置")
        print("2. 添加公众号")
        print("3. 更新认证信息")
        print("4. 退出")
        
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == "1":
            setup_project()
        elif choice == "2":
            add_wechat_account()
        elif choice == "3":
            update_auth_info()
        elif choice == "4":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
